<template>
  <div class="main-wrapper">
    <!-- 顶部查询与新增区 -->
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <el-form inline ref="searchForm" :model="searchForm" class="header-form">
          <el-form-item label="名称">
            <el-input
                v-model="searchForm.name"
                placeholder="请输入名称"
                clearable
                style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
                v-model="searchForm.remark"
                placeholder="请输入备注"
                clearable
                style="width: 200px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="loadData">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-button type="success" @click="openDialog()">新增</el-button>
          </el-form-item>
        </el-form>
      </template>
    </el-page-header>

    <div class="main-content">
      <el-table
          v-loading="loading"
          :data="tableData"
          style="width: 100%;"
          empty-text="无数据"
          :row-style="getRowStyle"
      >
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="name" label="名称(不可重复)" width="250" />
        <el-table-column prop="modelValue" label="模型值" width="200" />
        <el-table-column label="response_format" width="150">
          <template #default="{ row }">
            {{ getResponseFormat(row) }}
          </template>
        </el-table-column>
        <el-table-column prop="enableNormalQsTwoRequest" label="是否开启普通两轮询问" width="180">
          <template #default="{ row }">
            {{ row.enableNormalQsTwoRequest ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="排序权重" width="100" />
        <el-table-column prop="content" label="其他参数">
          <template #default="{ row }">
            <div class="simple-link" @click="showJson(row.content)">
              {{ row?.content ?? '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" width="150">
          <template #default="{ row }">
            <el-tooltip
                class="item"
                effect="dark"
                :content="row.remark || ''"
                placement="top"
            >
              <span>
                {{ (row.remark || '').length > 30
                  ? (row.remark || '').slice(0, 30) + '...'
                  : row.remark }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="220" />
        <el-table-column label="操作" fixed="right" width="300">
          <template #default="{ row }">
            <el-link type="primary" @click="openDialog(row)">编辑</el-link>
            <el-divider direction="vertical" />
            <el-link type="primary" @click="$refs.promptManager.show(row)">提示词</el-link>
            <el-divider direction="vertical" />
            <el-link type="primary" @click="setDefault(row.id)">设为默认</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="onDelete(row.id)">删除</el-link>
            <el-divider direction="vertical" />
            <el-link type="success" @click="copyRecord(row)">复制</el-link>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div class="footer-bar">
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :current-page="pageNumber"
          :total="total"
          @current-change="handlePageChange"
      />
    </div>

    <!-- JSON 查看对话框 -->
    <el-dialog
        title="查看 JSON 数据"
        v-model="jsonDialogVisible"
        width="800px"
    >
      <json-viewer :value="currentJson"/>
      <el-button icon="CopyDocument" type="primary" @click="copyJson">复制</el-button>
      <template #footer>
        <el-button @click="jsonDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 新增/编辑对话框 -->
    <el-dialog
        :title="isEdit ? '编辑记录' : '新增记录'"
        v-model="dialogVisible"
        width="700px"
    >
      <el-form
          ref="dialogForm"
          :model="dialogForm"
          :rules="dialogRules"
          label-width="130px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入名称"/>
        </el-form-item>
        <el-form-item label="模型值" prop="modelValue">
          <el-autocomplete
              v-model="dialogForm.modelValue"
              :fetch-suggestions="fetchModelValueSuggestions"
              placeholder="请输入或选择模型值"
          />
        </el-form-item>
        <el-form-item label="类型选择" prop="typeSelection">
          <el-radio-group v-model="dialogForm.typeSelection" @change="handleTypeChange">
            <el-radio :label="'jsonobject'">JsonObject</el-radio>
            <el-radio :label="'jsonschema'">JsonSchema</el-radio>
            <el-radio :label="'text'">Text</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="temperature">
          <el-slider v-model="dialogForm.temperature" :min="0" :max="2" :step="0.01" style="width: 70%; display: inline-block;" />
          <span style="margin-left: 12px; width: 50px; display: inline-block;">{{ dialogForm.temperature }}</span>
          <span style="margin-left: 8px; color: #888;">(0-2)</span>
        </el-form-item>
        <el-form-item label="top_p">
          <el-slider v-model="dialogForm.top_p" :min="0" :max="1" :step="0.01" style="width: 70%; display: inline-block;" />
          <span style="margin-left: 12px; width: 50px; display: inline-block;">{{ dialogForm.top_p }}</span>
          <span style="margin-left: 8px; color: #888;">(0-1)</span>
        </el-form-item>
        <el-form-item label="thinking">
          <el-select
            v-model="dialogForm.thinking"
            style="width: 100%;"
            :disabled="!isThinkingSupported"
            @click.native="handleThinkingClick"
          >
            <el-option label="disabled" value="disabled" />
            <el-option label="enabled" value="enabled" />
            <el-option label="auto" value="auto" />
          </el-select>
        </el-form-item>
        <el-form-item label="普通题目两轮询问" prop="enableNormalQsTwoRequest">
          <el-switch v-model="dialogForm.enableNormalQsTwoRequest" active-text="开启" inactive-text="关闭" />
        </el-form-item>
        <el-form-item label="权重" prop="weight">
          <el-input-number
              v-model="dialogForm.weight"
              :min="0"
              style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="其他参数" prop="content">
          <el-input
              type="textarea"
              rows="4"
              v-model="dialogForm.content"
              placeholder="请输入其他参数(应为JSON)"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="dialogForm.remark" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
            type="primary"
            :loading="dialogLoading"
            @click="submitDialog"
        >确定</el-button>
      </template>
    </el-dialog>

    <prompt-manager ref="promptManager" @refresh="loadData"></prompt-manager>
  </div>
</template>

<script>
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';
import PromptManager from './PromptManager.vue'


const thinkingModels = [
  'doubao-seed-1-6-250615',
  'doubao-seed-1-6-flash-250615'
];

export default {
  name: 'ModelRequestManager',
  components: { JsonViewer, PromptManager },
  data() {
    return {
      searchForm: { name: '', remark: '' },
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      // JSON 查看
      jsonDialogVisible: false,
      currentJson: null,
      // 新增/编辑
      dialogVisible: false,
      isEdit: false,
      dialogLoading: false,
      dialogForm: {
        id: null,
        name: '',
        modelValue: '',
        jsonobject: false,
        jsonschema: false,
        typeSelection: 'text',
        content: '',
        remark: '',
        weight: this.maxEmptyWight || 0,
        temperature: 0,
        top_p: 0.7,
        thinking: 'disabled',
        enableNormalQsTwoRequest: false // 默认 false
      },
      dialogRules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        content: [{ validator: this.validateJson, trigger: 'blur' }]
      },
      maxEmptyWight: 0,
      default: null,
      colorPalette: [
        '#e0ece0',
        '#f1f6f0',
        '#eef5fb',
        '#ECEFF1'
      ],
      nameColorMap: {},
      colorPaletteIdx: 0,
      defaultParams: {
        thinking: 'disabled',
        temperature: 0,
        top_p: 0.7
      },
      defaultTemperature: 1,
      defaultTopP: 0.7,
      isSyncingFromContent: false,
      isSyncingFromControl: false,
    };
  },
  computed: {
    isThinkingSupported() {
      return this.thinkingModels.includes(this.dialogForm.modelValue);
    },
    showThinking() {
      // 已废弃，始终显示thinking
      return true;
    },
    thinkingModels() {
      return thinkingModels;
    }
  },
  methods: {
    handleThinkingClick(e) {
      if (!this.isThinkingSupported) {
        this.$message.info('本模型不支持本参数');
        // 阻止下拉展开
        e.stopPropagation();
      }
    },
    copyRecord(row) {
      // 构造新的名称：原名 + 时间戳
      const now = new Date();
      const pad = n => n.toString().padStart(2, '0');
      const ts = `${now.getFullYear()}${pad(now.getMonth()+1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}`;
      const newName = `${row.name}_${ts}`;

      // 深拷贝一份数据并清除 id
      const payload = {
        ...row,
        id: null,
        name: newName
      };
      // 如果有 jsonobject/jsonschema 字段，需要保留其布尔值：
      payload.jsonobject = row.jsonobject;
      payload.jsonschema = row.jsonschema;

      // 调用新增接口
      this.$confirm(`确定复制"${row.name}"吗？`, '复制确认', { type: 'warning' })
          .then(() => {
            this.$axios.post('/api/model-request/add', payload)
                .then(() => {
                  this.$message.success('复制并新增成功');
                  this.loadData();
                })
                .catch(() => {
                  this.$message.error('复制失败');
                });
          })
          .catch(() => {});
    },
    async copyJson() {
      // 如果 currentJson 已经是字符串，就直接复制，否则格式化成漂亮的 JSON
      const text = typeof this.currentJson === 'string'
          ? this.currentJson
          : JSON.stringify(this.currentJson, null, 2);

      try {
        // 优先使用现代 Clipboard API
        await navigator.clipboard.writeText(text);
        this.$message.success('JSON 已复制到剪贴板');
      } catch (e) {
        // fallback for older browsers
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
          document.execCommand('copy');
          this.$message.success('JSON 已复制到剪贴板');
        } catch {
          this.$message.error('复制失败，请手动复制');
        }
        document.body.removeChild(textarea);
      }
    },
    calculateNameColorMap(data) {
      const freq = {};
      data.forEach(row => {
        // 按名称前6个数字分组
        let groupKey = '';
        if (row.name) {
          const match = String(row.name).match(/^\d{6}/);
          groupKey = match ? match[0] : '';
        }
        if (groupKey) {
          freq[groupKey] = (freq[groupKey] || 0) + 1;
        }
      });
      const map = {};
      let idx = 0;
      const colorPalette = this.colorPalette;
      data.forEach(row => {
        let groupKey = '';
        if (row.name) {
          const match = String(row.name).match(/^\d{6}/);
          groupKey = match ? match[0] : '';
        }
        if (groupKey && freq[groupKey] > 1 && !map[groupKey]) {
          map[groupKey] = colorPalette[idx % colorPalette.length];
          idx++;
        }
      });
      this.nameColorMap = map;
      this.colorPaletteIdx = idx;
    },
    getRowStyle({ row }) {
      // 默认模型优先高亮为红色
      if (this.default && this.default.id && row.id === this.default.id) {
        return { background: '#ffe8e8' };
      }
      let groupKey = '';
      if (row.name) {
        const match = String(row.name).match(/^\d{6}/);
        groupKey = match ? match[0] : '';
      }
      const bg = this.nameColorMap[groupKey];
      return bg ? { background: bg } : {};
    },
    goBack() {
      this.$router.back();
    },
    loadData() {
      this.loading = true;
      this.$axios.post('/api/model-request/selectPage', {
        page: { pageNumber: this.pageNumber, pageSize: this.pageSize, searchCount: true },
        name: this.searchForm.name,
        remark: this.searchForm.remark
      }).then(res => {
        const d = res.data;
        this.calculateNameColorMap(d.records || []);
        this.tableData = d.records || [];
        this.total = d.total || 0;
      }).finally(() => {
        this.loading = false;
      });

      this.$axios.get('/api/model-request/getMaxEmptyWeight')
          .then(res => {
            this.maxEmptyWight = res.data;
          });

      this.$axios.get('/api/model-request/default/get')
          .then(res => {
            this.default = res.data;
          });

      this.$refreshConfig()
    },
    reset() {
      this.searchForm = { name: '', remark: '' };
      this.pageNumber = 1;
      this.loadData();
    },
    handlePageChange(page) {
      this.pageNumber = page;
      this.loadData();
    },
    setDefault(id) {
      this.$confirm('确认设置为默认模型？', '警告', { type: 'warning' })
          .then(() => this.$axios.post(`/api/model-request/default/update/${id}`))
          .then(() => {
            this.$message.success('设置默认模型');
            this.loadData();
          })
          .catch(() => {});
    },
    openDialog(row = null) {
      if (row) {
        this.isEdit = true;
        this.dialogForm = { ...row };
        if (this.dialogForm.jsonobject) {
          this.dialogForm.typeSelection = 'jsonobject';
        } else if (this.dialogForm.jsonschema) {
          this.dialogForm.typeSelection = 'jsonschema';
        } else {
          this.dialogForm.typeSelection = 'text';
        }
        // 编辑时，content和控件同步
        this.$nextTick(() => {
          this.syncControlFromContent(this.dialogForm.content);
          this.syncContentFromControl();
        });
      } else {
        this.isEdit = false;
        // 新增时，content只包含thinking（如果有），不包含temperature和top_p
        const hasThinking = this.thinkingModels.includes(this.dialogForm.modelValue);
        const obj = {};
        if (hasThinking) {
          obj.thinking = { type: 'disabled' };
        }
        this.dialogForm = {
          id: null,
          name: '',
          modelValue: '',
          jsonobject: false,
          jsonschema: false,
          typeSelection: 'text',
          content: JSON.stringify(obj, null, 2),
          remark: '',
          weight: this.maxEmptyWight || 0,
          temperature: this.defaultTemperature,
          top_p: this.defaultTopP,
          thinking: 'disabled',
        };
      }
      this.dialogVisible = true;
    },
    handleTypeChange(value) {
      this.dialogForm.jsonobject = (value === 'jsonobject');
      this.dialogForm.jsonschema = (value === 'jsonschema');
    },
    getResponseFormat(row) {
      if (row.jsonobject) return 'JsonObject';
      if (row.jsonschema) return 'JsonSchema';
      return 'Text';
    },
    validateJson(rule, value, callback) {
      if (!value) return callback();
      try {
        JSON.parse(value);
        callback();
      } catch {
        callback(new Error('其他参数必须为合法的 JSON 字符串'));
      }
    },
    fetchModelValueSuggestions(queryString, cb) {
      this.$axios.post('/api/model-request/selectPage', {
        page: { pageNumber: 1, pageSize: 50, searchCount: false },
        name: '',
        remark: ''
      }).then(res => {
        const values = Array.from(new Set(
            res.data.records.map(r => r.modelValue)
        ));
        const suggestions = values
            .filter(val => val.includes(queryString))
            .map(val => ({ value: val }));
        cb(suggestions);
      });
    },
    submitDialog() {
      this.$refs.dialogForm.validate(valid => {
        if (!valid) return;
        this.dialogLoading = true;
        const api = this.isEdit ? '/api/model-request/update' : '/api/model-request/add';
        this.$axios.post(api, this.dialogForm)
            .then(() => {
              this.$message.success(this.isEdit ? '更新成功' : '新增成功');
              this.dialogVisible = false;
              this.loadData();
            })
            .finally(() => {
              this.dialogLoading = false;
            });
      });
    },
    onDelete(id) {
      this.$confirm('确认删除该记录？', '警告', { type: 'warning' })
          .then(() => this.$axios.get(`/api/model-request/delete?id=${id}`))
          .then(() => {
            this.$message.success('删除成功');
            this.loadData();
          })
          .catch(() => {});
    },
    showJson(jsonStr) {
      try {
        this.currentJson = JSON.parse(jsonStr);
      } catch {
        this.currentJson = jsonStr;
        this.$message.warning('无法解析为 JSON');
      }
      this.jsonDialogVisible = true;
    },
    syncContentFromControl() {
      this.isSyncingFromControl = true;
      const { thinking, temperature, top_p, modelValue } = this.dialogForm;
      let obj = {};
      try {
        obj = this.dialogForm.content ? JSON.parse(this.dialogForm.content) : {};
      } catch { obj = {}; }
      // 只有非默认值才写入 temperature 和 top_p
      if (temperature !== this.defaultTemperature) {
        obj.temperatrue = temperature;
      } else {
        delete obj.temperatrue;
      }
      if (top_p !== this.defaultTopP) {
        obj.top_p = top_p;
      } else {
        delete obj.top_p;
      }
      // thinking按条件有无
      if (this.thinkingModels.includes(modelValue)) {
        obj.thinking = { type: thinking };
      } else {
        delete obj.thinking;
      }
      // 如果 obj 为空，content 设为空字符串，否则格式化
      this.dialogForm.content = Object.keys(obj).length === 0 ? '' : JSON.stringify(obj, null, 2);
      this.isSyncingFromControl = false;
    },
    syncControlFromContent(val) {
      this.isSyncingFromContent = true;
      try {
        const obj = val ? JSON.parse(val) : {};
        if (typeof obj === 'object' && obj !== null) {
          // thinking
          if (obj.thinking && typeof obj.thinking === 'object' && 'type' in obj.thinking) {
            this.dialogForm.thinking = obj.thinking.type;
          } else {
            this.dialogForm.thinking = 'disabled';
          }
          // top_p
          if ('top_p' in obj) {
            this.dialogForm.top_p = obj.top_p;
          } else {
            this.dialogForm.top_p = this.defaultTopP;
          }
          // temperatrue
          if ('temperatrue' in obj) {
            this.dialogForm.temperature = obj.temperatrue;
          } else {
            this.dialogForm.temperature = this.defaultTemperature;
          }
        }
      } catch {
        this.dialogForm.thinking = 'disabled';
        this.dialogForm.top_p = this.defaultTopP;
        this.dialogForm.temperature = this.defaultTemperature;
      }
      this.isSyncingFromContent = false;
    }
  },
  created() {
    this.loadData();
  },
  beforeUnmount() {
    this.$refreshConfig()
  },
  watch: {
    'dialogForm.modelValue'(val) {
      // 切换模型时，若支持thinking则加上，否则移除
      this.syncContentFromControl();
    },
    'dialogForm.temperature'(val) {
      if (this.isSyncingFromContent) return;
      this.syncContentFromControl();
    },
    'dialogForm.top_p'(val) {
      if (this.isSyncingFromContent) return;
      this.syncContentFromControl();
    },
    'dialogForm.thinking'(val) {
      if (this.isSyncingFromContent) return;
      this.syncContentFromControl();
    },
    'dialogForm.content'(val) {
      if (this.isSyncingFromControl) return;
      this.syncControlFromContent(val);
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.highlight-default-row) {
  background-color: #ffe8e8 !important;
}
.simple-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.simple-link:hover {
  color: #40a9ff;
}

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    flex-shrink: 0;

    .header-form {
      :deep(.el-form-item) {
        margin-bottom: 0;
      }
    }
  }

  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

// 动态行背景色
:deep(.row-bg-) {
  background-color: inherit;
}
:deep(.row-bg-doubao) {
  background-color: #e0ece0 !important;
}
:deep(.row-bg-doubas) {
  background-color: #f1f6f0 !important;
}
:deep(.row-bg-douba1) {
  background-color: #eef5fb !important;
}
:deep(.row-bg-douba2) {
  background-color: #ECEFF1 !important;
}
</style>

