<template>
  <el-dialog
      title="模型详情"
      v-model="detailDialogVisible"
      width="600px"
      @close="close"
  >
    <el-descriptions
        :column="1"
        border
    >
      <el-descriptions-item label="ID">
        {{ detailForm.modelRequestId }}
      </el-descriptions-item>
      <el-descriptions-item label="名称">
        {{ detailForm.label }}
      </el-descriptions-item>
      <el-descriptions-item label="模型值">
        {{ detailForm.value }}
      </el-descriptions-item>
      <el-descriptions-item label="输出类型">
        {{ getResponseFormat(detailForm) }}
      </el-descriptions-item>
      <el-descriptions-item label="其他参数">
        <json-viewer :value="parseJson(detailForm.content)" expand-depth="2"/>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-button @click="detailDialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>
<script >
import { JsonViewer } from 'vue3-json-viewer';
import 'vue3-json-viewer/dist/index.css';

export default {
  components: {
    JsonViewer
  },
  data() {
    return {
      dialogLoading: false,
      dialogForm: { /* ... */ },
      detailDialogVisible: false,
      detailForm: {}
    };
  },
  methods:{
    parseJson(str) {
      try {
        return JSON.parse(str);
      } catch {
        return str;
      }
    },
    getResponseFormat(row) {
      if (row.jsonobject) return 'JsonObject';
      if (row.jsonschema) return 'JsonSchema';
      return 'Text';
    },
    show(e) {
      this.detailDialogVisible = true;
      this.detailForm = e;
    },
    close() {
      this.detailDialogVisible = false;
      this.detailForm = {};
    }
  }
}
</script>