<template>
  <div style="width: 100%;height: 100%">
    <el-form :model="form" ref="formRef" style="width: 100%;height: 100%">
      <div class="container">
        <el-page-header class="header-bar" @back="onBack">
          <template #content>
            <div style="display: flex">
              <el-image src="/icon/16.png" class="icon"></el-image>
              <el-text class="text">配标准卷</el-text>
            </div>
          </template>
        </el-page-header>
        <div class="bottom-area">
          <div class="left-content">
            <!--            <el-image src="/icon/correctPackageUploadStep1.svg" class="step"></el-image>-->
            <!--            <el-button-->
            <!--                style="background: transparent;border: none;width: 540px;height: 42px;z-index: 9999;position: fixed;top: 125px;left: 210px"-->
            <!--                ref="yinDaoRef1" id="yinDaoRef1"></el-button>-->
            <!--            <el-button-->
            <!--                style="background: transparent;border: none;width: 480px;height: 42px;z-index: 9999;position: fixed;top: 125px;left: 750px"-->
            <!--                ref="yinDaoRef2" id="yinDaoRef2"></el-button>-->

            <div class="description" style="margin-top: 0px">
              <div class="divider"></div>
              <div class="title">选择上传方式：</div>
            </div>
            <div class="bottom">
              <div class="upload-area">
                <el-upload class="upload-container" ref="remoteUploaderRef"
                           :with-credentials="true"
                           :show-file-list="true"
                           :auto-upload="false"
                           :file-list="remoteFileList">
                  <div class="upload-item" @click.stop="onRemoteFileSelect">
                    <el-image src="/icon/upload2.svg" class="left-content"></el-image>
                    <div class="right-content">
                      <div class="title">打印机文件上传</div>
                      <div class="subtitle">支持多份试卷批量上传</div>
                    </div>
                  </div>
                </el-upload>

                <el-upload class="upload-container" ref="pdfUploader" accept=".pdf"
                           drag
                           :data="getParseConfig"
                           :action="$fileserver.pdf2imgUrl"
                           :with-credentials="true"
                           :show-file-list="true"
                           :auto-upload="false"
                           :file-list="docList"
                           :on-change="onDocChange"
                           :on-success="uploadDoc"
                           :on-start="onUploadStart"
                           :on-progress="onUploadProgress"
                >
                  <div class="upload-item">
                    <el-image src="/icon/upload1.svg" class="left-content"></el-image>
                    <div class="right-content">
                      <div class="title">文件上传</div>
                      <div class="subtitle">请点击文件上传到此处上传
                      </div>
                      <div class="speed-text">传输速度：{{ uploadSpeed ? uploadSpeed : '---' }}</div>
                    </div>
                  </div>

                </el-upload>
              </div>

              <el-divider style="width: 1050px;margin-top: 40px"></el-divider>

            </div>
            <div class="description">
              <div class="divider"></div>
              <div class="title">文件信息：</div>
            </div>
            <div class="file-area">
              <div style="position: relative;">
                <div class="hint-text">例如：250516-转塘-数学-602-配置</div>
                <el-form-item label="样卷名称:" prop="name" label-width="82px">
                  <el-input class="item" v-model="form.name"/>
                </el-form-item>
              </div>
              <div style="position: relative;">
                <div class="hint-text">对应循环页数</div>
                <el-form-item label="样卷页数：" prop="pageNum" label-width="82px">
                  <el-input-number class="item" :min="1" :controls="false" v-model="form.pageNum"
                                   :value-on-clear="1"/>
                </el-form-item>
              </div>
              <el-form-item label="文件大小：" label-width="82px">
                <el-input class="item" v-model="fileInformation.size" disabled/>
              </el-form-item>
              <el-form-item label="试卷页数：" label-width="82px">
                <el-input-number class="item" :min="1" :controls="false" v-model="fileInformation.cnt"
                                 :value-on-clear="1" disabled/>
              </el-form-item>

              <el-form-item label="样卷名称：" label-width="82px">
                <el-input class="item" v-model="fileInformation.name" disabled/>
              </el-form-item>

              <el-form-item label="预计耗时：" label-width="82px">
                <el-input class="item" v-model="fileInformation.correctTime" disabled/>
              </el-form-item>
            </div>
          </div>
          <div class="right-content">
            <cropper
                ref="cropper"
                class="canvas"
                :loading="loadingCropper"
                :is-empty="!cropperImageList.length"
                :default-isFreeze-keyboard-withoutForce="true"
                @arrow-key-pressed="arrowKeyPressed">
            </cropper>
            <div class="pagination" v-show="cropperImageList.length">
              <el-pagination
                  background
                  layout="prev, pager, next"
                  :total="cropperImageList.length"
                  v-model:current-page="nowCropperPageIdx"
                  :page-size="1"
                  class="right"
              />
            </div>
          </div>
        </div>

        <div style="flex-grow: 1;"></div>
        <div class="bottom-button-area">
          <el-button class="confirm-button" @click="onSubmit">下一步</el-button>
          <el-button class="cancel-button" @click="onBack">取消</el-button>
        </div>
      </div>
    </el-form>

    <!-- 远程文件 -->
    <file-selector ref="remoteFileSelectorRef" type="upload" @file-selected="onRemoteFileSelected"/>

    <!--    <el-tour v-model="needPackageConfigGuide" type="primary" :mask="false" @close="finishTour">-->
    <!--      <el-tour-step target="#yinDaoRef1" title="上传样卷" description="首先需要上传样卷，并填写样卷名称">-->
    <!--      </el-tour-step>-->
    <!--      <el-tour-step-->
    <!--          target="#yinDaoRef2"-->
    <!--          title="配置试卷"-->
    <!--          description="在此配置上传好的样卷"-->
    <!--      />-->
    <!--    </el-tour>-->
  </div>
</template>

<script>
import {ElLoading} from "element-plus";
import FileSelector from "@/views/common/FileSelector.vue";
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store";
import Cropper from "@/components/cropper/forPreview.vue";
import {img2base64} from "@/utils/imgUtil";
import * as pdfjsLib from "pdfjs-dist";

const store = useUserStore();
export default {
  components: {
    Cropper,
    FileSelector,
  },
  data() {
    return {
      title: "开始批改",
      form: {
        name: "",
        pageNum: 2,
      },
      docList: [],
      action: "correct",
      loadingInstance: null,
      fileInformation: {
        size: "",
        cnt: null,
        correctTime: null,
        name: null,
        pageRealNum: null,
      },
      filename: null,
      packageIds: [],
      packageId: null,
      fileUploading: false,
      remoteFileList: [],
      cropperImageList: [],
      nowCropperPageIdx: 1,
      loadingCropper: false,
      imgDataUrl: null,
      imageList: {},
      uploadSpeed: "",
      _lastLoaded: 0,
      _lastTimestamp: 0,
      imagesList: {},
      convertIdx: 0,
      selectRemoteFileRow: null,
      paperType: null,
      ftpMessageFileDetail: null,
      ftpMessageFileId: null,
    };
  },
  mounted() {
    this.form.name = this.getBaseName();
    this.ftpMessageFileId = this.$route.query?.ftpMessageFileId ?? null;
    const fileDetail = this.$route.query?.ftpMessageDetail ?? null;
    if (fileDetail) {
      const fileDetailJsonString = decodeURIComponent(fileDetail);
      this.ftpMessageFileDetail = JSON.parse(fileDetailJsonString);
      this.initByFtpMessage()
      if (this.ftpMessageFileDetail?.formFileName) {
        this.form.name = this.getBaseName() + this.ftpMessageFileDetail?.formFileName;
      }
    }
  },
  computed: {
    ...mapState(useUserStore, ["needPackageConfigGuide"]),
    containerStyles() {
      if (this.fileInformation.size)
        return {
          backgroundColor: "#efeeff",
          borderColor: "#332CF5",
          borderWidth: "thin",
          borderStyle: "solid",
        };
      else return {};
    },
  },
  watch: {
    nowCropperPageIdx(val) {
      this.showNowImage()
    },
    "form.pageNum"(val) {
      this.convertSinglePdfUrlToBase64();
    },
  },
  methods: {
    initByFtpMessage() {
      const ftpMessageFileDetail = this.ftpMessageFileDetail;
      let obj = {
        name: ftpMessageFileDetail.name,
        percentage: 100,
        status: 'success',
        url: ftpMessageFileDetail.url
      }
      this.onRemoteFileSelected(obj, ftpMessageFileDetail)
    },
    arrowKeyPressed(e) {
      let pageIdx = this.nowCropperPageIdx;
      if (e === 1) {
        if (pageIdx === this.cropperImageList.length) {
          this.$message.warning("已经是最后一页");
        } else {
          this.nowCropperPageIdx = e + pageIdx;
        }
      } else if (e === -1) {
        if (pageIdx === 1) {
          this.$message.warning("已经是第一页");
        } else {
          this.nowCropperPageIdx = e + pageIdx;
        }
      }
    },
    async convertSinglePdfUrlToBase64(needResetCropperImageList = true) {
      let pdfUrl = this.filename;
      if (!pdfUrl) return;
      if (needResetCropperImageList) {
        this.cropperImageList = [];
      }
      this.loadingCropper = true;
      const path = pdfUrl.startsWith('/')
          ? `/static${pdfUrl}`
          : `/static/${pdfUrl}`;
      let loadingMessage = this.$message({
        message: "预览加载中...",
        icon: "Loading",
        type: "warning",
        duration: 0,
      })
      const response = await fetch(this.$fileserver.fileurl(path));
      if (!response.ok) {
        throw new Error('Failed to fetch PDF');
      }
      const blob = await response.blob();
      const pdfData = await blob.arrayBuffer();
      const pdfDoc = await pdfjsLib.getDocument(pdfData).promise;
      const totalPages = pdfDoc.numPages;
      const targetDPI = 300;
      const scaleFactor = targetDPI / 72;
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');

      // 遍历每一页，将其转换为 Base64 图像
      for (let i = 1; i <= totalPages && i <= this.form.pageNum; i++) {
        loadingMessage.close();
        if (i - 1 < this.cropperImageList.length) {
          continue;
        }
        const page = await pdfDoc.getPage(i);
        const viewport = page.getViewport({scale: scaleFactor});
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        if (i === 1) {
          let widthMM  = (viewport.width  / scaleFactor)  * 25.4 / 72;
          let heightMM = (viewport.height / scaleFactor) * 25.4 / 72;
          if (widthMM > heightMM) {
            let temp = widthMM;
            widthMM = heightMM;
            heightMM = temp;
          }
          let paperType = store.getDefaultDocType;
          if (
              (widthMM  >= 208 && widthMM  <= 212 && heightMM >= 295 && heightMM <= 299) ||
              (heightMM >= 208 && heightMM <= 212 && widthMM  >= 295 && widthMM  <= 299)
          ) {
            paperType = 'A4';
          }
          else if (
              (widthMM  >= 295 && widthMM  <= 300 && heightMM >= 418 && heightMM <= 422) ||
              (heightMM >= 295 && heightMM <= 300 && widthMM  >= 418 && widthMM  <= 422)
          ) {
            paperType = 'A3';
          }
          else if (
              (widthMM  >= 193 && widthMM  <= 197 && heightMM >= 268 && heightMM <= 272) ||
              (heightMM >= 193 && heightMM <= 197 && widthMM  >= 268 && widthMM  <= 272)
          ) {
            paperType = '16K';
          }
          else if (
              (widthMM  >= 258 && widthMM  <= 262 && heightMM >= 368 && heightMM <= 372) ||
              (heightMM >= 258 && heightMM <= 262 && widthMM  >= 368 && widthMM  <= 372)
          ) {
            paperType = '8K';
          }
          this.paperType = paperType;
          console.log(`检测到 PDF 纸张类型：${paperType}`);
        }
        const renderPromise = page.render({
          canvasContext: context,
          viewport: viewport,
        }).promise;

        const axiosPromise = this.$axios.get(
            "/api/file/pdf2Img?filename=" + this.filename + "&pageNum=" + i
        );
        const [_, pdf2ImgResponse] = await Promise.all([renderPromise, axiosPromise]);

        if (!pdf2ImgResponse.data) {
          this.$message.error("文件转换失败");
          return;
        }
        let paperUrl = pdf2ImgResponse.data.url
        this.cropperImageList[i - 1] = {
          pdfUrl: pdf2ImgResponse.data.docpath,
          imageUrl: paperUrl
        };
        // 每页渲染完后"让渡"一下：0ms 可以让出主线程到下一轮事件循环
        await new Promise(resolve => setTimeout(resolve, 0));

        const imageBlob = await new Promise((resolve) => {
          canvas.toBlob(
              (blobResult) => {
                resolve(blobResult);
              },
              'image/jpeg',  // 可以根据需求改为 'image/png' 或其他格式
              0.92           // 质量参数 0~1，JPEG 专用。可省略，默认 0.92
          );
        });
        const objectUrl = URL.createObjectURL(imageBlob);
        store.setImageDataById(paperUrl, objectUrl);
        this.imagesList[paperUrl] = objectUrl;

        this.convertIdx = i;
        if (i === 1) {
          this.showNowImage();
        }
      }
      this.loadingCropper = false;
      this.convertIdx = 0;
    },
    preview() {
      this.$refs.cropper.setImg(this.imgDataUrl, [], []);
    },
    showNowImage() {
      const curr = this.cropperImageList[this.nowCropperPageIdx - 1];
      this.imgDataUrl = this.imagesList[curr.imageUrl];
      this.preview();
    },
    onBack() {
      this.$router.back();
    },
    onClose(id) {
      this.$refs.formRef.resetFields();
      this.docList = [];
      this.remoteFileList = [];
      this.isShow = false;
      this.$emit("finishUpload", id);
    },
    getBaseName(fileName) {
      const now = new Date();
      const year = now.getFullYear().toString().slice(-2);
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const datePrefix = `${year}${month}${day}-`;
      return datePrefix;
    },
    onSubmit() {
      if (this.docList.length === 0 && this.remoteFileList.length === 0) {
        this.$message.error("请上传试卷");
        return;
      }
      if (this.fileUploading) {
        this.$message.warning("请等待试卷上传完成");
        return;
      }
      this.$axios.get("/api/docCorrectConfigPackage/checkName?name=" + this.form.name).then((res) => {
        if (res.code === 200) {
          this.submit();
        }
      });
    },
    async submit() {
      this.loadingInstance = ElLoading.service({
        lock: true,
        text: "Loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let saveConfigDefaultForm = {
        areas: "[]",
        config: "{\"score\":true,\"scoreFormat\":1,\"scoreColor\":\"red\",\"scoreFontSize\":10,\"fontSize\":10,\"flagSize\":20,\"flagColor\":\"red\",\"errorFlagColor\":\"red\",\"errorFlagSize\":20,\"errorFlag\":\"x\",\"correctFlag\":\"a\",\"nameArea\":null,\"scoreArea\":null,\"additionalName\":\"附加\",\"prompt\":null,\"qsOcrPrompt\":null,\"scoreTypes\":[\"总分\"]}",
        docType: this.paperType || store.getDefaultDocType,
        docurl: "",
        img: "",
        name: "",
      };
      for (let pageNum = 1; pageNum <= this.form.pageNum; pageNum++) {
        if (pageNum - 1 < this.cropperImageList.length) {
          saveConfigDefaultForm.docurl = this.cropperImageList[pageNum - 1].pdfUrl;
          saveConfigDefaultForm.img = this.cropperImageList[pageNum - 1].imageUrl;
        } else {
          const pdf2ImgResponse = await this.$axios.get("/api/file/pdf2Img?filename=" + this.filename + "&pageNum=" + pageNum);
          if (!pdf2ImgResponse.data) {
            this.$message.error("文件转换失败");
            return;
          }
          saveConfigDefaultForm.docurl = pdf2ImgResponse.data.docpath;
          saveConfigDefaultForm.img = pdf2ImgResponse.data.url;
        }
        saveConfigDefaultForm.name = this.form.name + "_页" + pageNum;
        const saveConfigResponse = await this.$axios.post("/api/docCorrectConfig/update", saveConfigDefaultForm);
        if (!saveConfigResponse.data) {
          this.$message.error("配置保存失败");
          return;
        }
        this.packageIds.push(saveConfigResponse.data.id);
      }
      const createForm = {
        name: this.form.name,
        config: JSON.stringify(this.packageIds),
      };
      const savePackageConfigResponse = await this.$axios.post("/api/docCorrectConfigPackage/add", createForm);
      if (!savePackageConfigResponse.data) {
        this.$message.error("配置保存失败");
        return;
      }
      if (this.selectRemoteFileRow) {
        const row = this.selectRemoteFileRow;
        const param = {
          id: row?.id,
          remark: this.form.name,
          filename: row.name,
          modified: row.modified,
          path: row.path,
          size: row.size,
          type: 'configPackage',
          configPackageId: savePackageConfigResponse.data.id
        }
        await this.$axios.post(`/api/ftpFilesRemark/${'remark' in row ? 'update' : 'add'}`, param);
      }

      this.packageId = savePackageConfigResponse.data.id;
      this.loadingInstance.close();
      this.$router.push(`/correctConfigPackages/stepConfigPapers/${this.packageId}`);
    },
    uploadDoc(response) {
      let files = response.data;
      if (!files || files.length === 0) {
        this.$message.error("文件上传失败!");
        return;
      }
      this.filename = response.data.filename;
      this.fileInformation.cnt = response.data.pageNum;
      this.fileInformation.pageRealNum = response.data.pageNum;
      this.fileInformation.correctTime = Math.floor(this.form.pageNum * 3) || 3 + "分钟";
      this.fileUploading = false;
      this.cropperImageList = [];
      if (response.data.pageNum <= 4 && this.form.pageNum !== response.data.pageNum) {
        this.form.pageNum = response.data.pageNum;
      } else {
        this.convertSinglePdfUrlToBase64();
      }
      this.uploadSpeed = "";
    },
    onDocChange(uploadFile) {
      if (uploadFile.percentage === 0) {
        this.fileUploading = true;
      }
      this.fileInformation.size = (uploadFile.size / 1024 / 1024).toFixed(2) + "MB";
      this.fileInformation.name = uploadFile.name?.split(".")[0] + "_配置";
      this.form.name = this.getBaseName() + uploadFile.name?.split(".")[0] + "_配置";
      let status = uploadFile.status;
      if (status === "ready") {
        this.docList = [uploadFile];
        this.form.name = this.getBaseName() + uploadFile.name?.split(".")[0];
      }
      this.uploadSpeed = "";
      this.$refs.pdfUploader.submit();
    },
    onUploadStart(file) {
      this._lastTimestamp = Date.now();
      this._lastLoaded = 0;
      this.uploadSpeed = "计算中...";
    },
    onUploadProgress(evt, file, fileList) {
      const now = Date.now();
      const elapsedMs = now - this._lastTimestamp;
      if (elapsedMs <= 0) return;
      const loaded = evt.loaded;
      const deltaLoaded = loaded - this._lastLoaded;
      const speedBytesPerSec = deltaLoaded / (elapsedMs / 1000);
      this._lastLoaded = loaded;
      this._lastTimestamp = now;
      let speedText = "";
      if (speedBytesPerSec < 1024) {
        speedText = `${speedBytesPerSec.toFixed(0)} B/s`;
      } else if (speedBytesPerSec < 1024 * 1024) {
        speedText = `${(speedBytesPerSec / 1024).toFixed(2)} KB/s`;
      } else {
        speedText = `${(speedBytesPerSec / (1024 * 1024)).toFixed(2)} MB/s`;
      }
      this.uploadSpeed = speedText;
    },
    getParseConfig() {
      return {
        checkpageNum: true,
      };
    },
    onRemoteFileSelect() {
      this.$refs.remoteFileSelectorRef.show();
    },
    onRemoteFileSelected(uploadFile, row) {
      this.selectRemoteFileRow = row;
      let filename = uploadFile.name.split(".")[0];
      this.form.name = this.getBaseName() + filename;
      this.remoteFileList = [uploadFile];
      const loading = this.$message.warning({
        message: "解析文件中...",
        duration: 0,
        icon: "loading",
      });
      this.$axios
          .post(
              "/api/file/upload/doc2img",
              {
                checkpageNum: true,
                filePath: uploadFile.url,
              },
              {
                headers: {
                  "Content-Type": "application/x-www-form-urlencoded",
                },
              }
          )
          .then((res) => {
            loading.close();
            this.$message.success("解析文件成功");
            this.uploadDoc(res);
          })
          .catch(() => {
            loading.close();
          });
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small) {
  margin-bottom: 5px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .header-bar {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .icon {
      width: 28.28px;
      height: 22.89px;
    }

    .text {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-left: 10px;
    }
  }

  .bottom-area {
    display: flex;
    width: 100%;
    gap: 20px;
    height: calc(100vh - 200px);

    .left-content {
      flex-shrink: 0;
      padding: 0;
      width: 720px;

      .description {
        display: flex;
        align-items: center;
        margin-left: 12px;

        .divider {
          width: 3px;
          height: 16px;
          top: 2.5px;
          border-radius: 2px 0 0 0;
          background: #3981FF;
        }

        .title {
          font-size: 15px;
          font-weight: bolder;
          line-height: 21px;
          text-align: left;
          margin-left: 6px;
        }
      }

      .bottom {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-top: 15px;
        margin-left: 12px;

        .upload-area {
          display: flex;
          gap: 30px;
          width: 100%;

          .upload-container {
            width: 330px;
            height: 82.6px;
            display: flex;
            flex-direction: column;
            border: none !important;
            
            :deep(.el-upload) {
              border: none !important;
            }

            :deep(.el-upload-dragger) {
              padding: 0;
              text-align: left;
              border: none !important;
              border-style: none !important;
            }

            .upload-item {
              width: 354px;
              height: 82.6px;
              background: #3981ff1a;
              border-radius: 6px;
              align-items: center;
              display: flex;
              cursor: pointer;
              transition: all 0.2s ease;
              
              &:hover {
                background: #3981ff2a;
                /* border: 2px solid #3981FF; */
                /* 去除边框加粗 */
                
                .right-content .title {
                  font-weight: 900;
                }
              }

              .el-upload-list {
                position: fixed !important;
                top: 10px;
              }

              .left-content {
                width: 60.58px;
                height: 58px;
                margin-left: 10px;
              }

              .right-content {
                display: flex;
                flex-direction: column;
                margin-left: 9px;
                width: 100%;
                align-items: flex-start;

                .title {
                  font-size: 16px;
                  color: #3981FF;
                  font-weight: bolder;
                  margin-top: 5px;
                  transition: font-weight 0.2s ease;
                }
                
                .title:hover {
                  font-weight: 900;
                }

                .subtitle {
                  font-size: 13px;
                  color: #999999;
                  letter-spacing: 0;
                }

                .speed-text {
                  font-size: 13px;
                  color: #409EFF;
                }
              }
            }
          }

        }
      }

      .file-area {
        margin-top: 29.5px;
        margin-left: 12px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        width: 800px;
        gap: 24px 68px;

        .hint-text {
          position: absolute;
          top: -20px;
          left: 90px;
          right: 0;
          font-size: 13px;
          color: #999999;
          font-style: italic;
          white-space: nowrap;
        }

        .item {
          width: 220px;
          height: 32px;
        }
      }

      .step {
        width: 1069.83px;
        height: 42px;
      }
    }

    .right-content {
      height: 100%;
      padding: 0 0 10px 0;
      width: 100%;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .canvas {
        height: 100%;
        width: 100%;
      }

      .pagination {
        height: 50px;
        margin-top: 5px;
        display: flex;
        align-items: center;

        .left {
          width: 200px;
          flex-shrink: 0;
        }

        .right {
          flex-grow: 1;
        }
      }
    }

  }


  .bottom-button-area {
    display: flex;
    height: 45px;
    width: 100%;
    flex-direction: row-reverse;
    gap: 24px;
    border-top: 2px solid #eeeeee;
    padding: 13px 0 0 0;

    .cancel-button {
      width: 74px;
      height: 32px;
      background: #FFFFFF;
      border: 1px solid #00000026;
      border-radius: 6px;

      .el-button__text {
        font-weight: 400;
        font-size: 14px;
        color: #000000e0;
        text-align: center;
        line-height: 22px;
      }
    }

    .confirm-button {
      width: 74px;
      height: 32px;
      background: #1677FF;
      border-radius: 6px;
      color: #FFFFFF !important;
    }
  }

}
</style>