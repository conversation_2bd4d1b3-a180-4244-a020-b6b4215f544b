<template>
  <div>
    <el-button type="primary" @click="$refs.addQuestionTypeDialog.show()">
      添加大题类型
    </el-button>

    <el-input
        v-model="filterText"
        placeholder="请输入关键字筛选"
        clearable
        style="margin: 20px 0;"
    />
    <el-radio-group v-model="selectedType" style="margin-bottom: 20px;">
      <el-radio
          v-for="type in promptTypes"
          :key="type"
          :label="type"
      >{{ type }}</el-radio>
    </el-radio-group>
    <div
        v-for="item in filteredPrompts"
        :key="item.key"
        class="prompt-item"
    >
      <div class="prompt-title">
        {{ item.desc }} ({{ item.key }})
        <el-tag v-if="
          (item.desc && (
            item.desc.includes('提示') ||
            item.desc.includes('模板')
          )) ||
          (item.type === '普通题目-大题类型') ||
          (item.type === '标准卷批注')
        " type="primary" style="margin-left: 8px;">string</el-tag>
        <el-tag v-else-if="item.desc && item.desc.includes('格式')" type="danger" style="margin-left: 8px;">json</el-tag>
      </div>
      <el-input
          type="textarea"
          v-model="item.value"
          autosize
          class="prompt-input"
      />
      <div class="prompt-actions">
        <el-button type="primary" @click="updatePrompt(item)">更改</el-button>
        <el-button type="warning" @click="resetPrompt(item)">重置</el-button>
        <el-button type="info" @click="showLog(item)">变更日志</el-button>
        <el-button type="danger" @click="deleteByKey(item)">删除</el-button>
      </div>
    </div>
    <el-dialog
        title="Prompt 变更日志"
        v-model="logDialogVisible"
        width="60%"
    >
      <el-table
          :data="changeLogs"
          border
          style="width: 100%;"
      >
        <el-table-column
            prop="createTime"
            label="变更时间"
            width="180"
        />
        <el-table-column prop="oldValue" label="修改前内容(点击复制)">
          <template #default="{ row }">
            <span class="copy-cell" @click="copyText(row.oldValue)">
{{
                row.oldValue.length > 40
                    ? row.oldValue.slice(0, 20) + '…………' + row.oldValue.slice(-20)
                    : row.oldValue
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="newValue" label="修改后内容(点击复制)">
          <template #default="{ row }">
            <span class="copy-cell" @click="copyText(row.newValue)">
      {{
                row.newValue.length > 40
                    ? row.newValue.slice(0, 20) + '…………' + row.newValue.slice(-20)
                    : row.newValue
              }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="logDialogVisible = false" style="margin-top: 20px">关闭</el-button>
      </span>
    </el-dialog>

    <AddQuestionTypeDialog ref="addQuestionTypeDialog" @close="fetchPrompts" />
  </div>
</template>

<script>
import { ElMessage } from 'element-plus';
import AddQuestionTypeDialog  from './AddQuestionTypeDialog.vue';

export default {
  name: 'PromptsPage',
  components: {
    AddQuestionTypeDialog
  },
  data() {
    return {
      prompts: [],
      filterText: '',
      selectedType: '全部',
      logDialogVisible: false,
      changeLogs: [],
      currentKey: ''
    };
  },
  computed: {
    promptTypes() {
      const types = new Set(this.prompts.map(item => item.type));
      return ['全部', ...types];
    },
    filteredPrompts() {
      let filtered = this.prompts;
      if (this.selectedType !== '全部') {
        filtered = filtered.filter(item => item.type === this.selectedType);
      }
      if (this.filterText) {
        const f = this.filterText.toLowerCase();
        filtered = filtered.filter(item =>
            item.key.toLowerCase().includes(f) ||
            item.desc.toLowerCase().includes(f)
        );
      }
      filtered = filtered.slice().sort((a, b) => {
        const aIsJson = (a.desc && a.desc.includes('格式'));
        const bIsJson = (b.desc && b.desc.includes('格式'));
        if (aIsJson === bIsJson) return 0;
        return aIsJson ? -1 : 1;
      });
      return filtered;
    }
  },
  mounted() {
    this.fetchPrompts();
  },
  methods: {
    /**
     * 删除指定的 Prompt 项
     */
    deleteByKey(item) {
      this.$confirm(
          `确定要删除 "${item.desc}" (${item.key}) 吗？`,
          '删除确认',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
      )
          .then(() => {
            return this.$axios.get('/api/prompts/deleteKey', {
              params: { key: item.key }
            });
          })
          .then(() => {
            this.fetchPrompts();
            ElMessage.success('删除成功');
          })
          .catch(err => {
            // 如果是用户取消或其他错误
            if (err !== 'cancel') {
              ElMessage.error('删除失败，请重试');
            }
          });
    },
    fetchPrompts() {
      this.$axios.get('/api/prompts/list')
          .then(res => {
            const data = res.data;
            this.prompts = Object.keys(data).map(key => ({
              key,
              desc: data[key].desc,
              value: data[key].value,
              type: data[key].type
            }));
          })
          .catch(() => {
            ElMessage.error('获取 prompt 列表失败');
          });
    },
    updatePrompt(item) {
      this.$axios.post('/api/prompts/update', {
        key: item.key,
        value: item.value
      })
          .then(() => {
            ElMessage.success('更新成功');
            this.fetchPrompts();
          })
          .catch(() => {
            ElMessage.error('更新失败');
          });
    },
    resetPrompt(item) {
      this.$axios.get('/api/prompts/reset', {
        params: { key: item.key }
      })
          .then(() => {
            ElMessage.success('重置成功');
            this.fetchPrompts();
          })
          .catch(() => {
            ElMessage.error('重置失败');
          });
    },
    showLog(item) {
      this.currentKey = item.key;
      this.logDialogVisible = true;
      const dto = {
        promptKey: item.key,
        page: { pageNumber: 1, pageSize: 100 }
      };
      this.$axios.post('/api/promptChangeLog/page', dto)
          .then(res => {
            this.changeLogs = res.data.records || [];
          })
          .catch(() => {
            ElMessage.error('获取变更日志失败');
          });
    },
    copyText(text) {
      if (!text) return;
      navigator.clipboard.writeText(text)
          .then(() => {
            ElMessage.success('已复制到剪贴板');
          })
          .catch(() => {
            ElMessage.error('复制失败');
          });
    }
  }
};
</script>

<style scoped>
.prompt-item {
  margin-bottom: 20px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.prompt-title {
  font-weight: bold;
  margin-bottom: 10px;
}
.prompt-input {
  width: 100%;
}
.prompt-actions {
  margin-top: 10px;
}
.copy-cell {
  cursor: pointer;
  color: #409EFF;
  white-space: pre-wrap;
}
</style>
